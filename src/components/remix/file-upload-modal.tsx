'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Upload, Play, Pause, Download, Trash2, Disc, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRemixModalTheme } from '@/app/remix/utils/modal-theme';
import {
  UploadFile,
  validateFiles,
  createUploadFiles,
  uploadFile as uploadFileUtil,
  formatFileSize,
  formatDuration,
  createAudioUrl,
  cleanupAudioUrl
} from '@/lib/file-upload-utils';

interface FileUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (files: UploadFile[]) => void;
}

export function FileUploadModal({ isOpen, onClose, onSubmit }: FileUploadModalProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [applicationMessage, setApplicationMessage] = useState('');
  const [playingFileId, setPlayingFileId] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<Map<string, HTMLAudioElement>>(new Map());
  const [fileDurations, setFileDurations] = useState<Map<string, number>>(new Map());
  const [isSubmitted, setIsSubmitted] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get remix theme for modal
  const { cssVariables, themeMode } = useRemixModalTheme();



  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFiles = useCallback((files: File[]) => {
    const { validFiles, errors: validationErrors } = validateFiles(files, uploadFiles);
    
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setErrors([]);
    const newUploadFiles = createUploadFiles(validFiles);
    setUploadFiles(prev => [...prev, ...newUploadFiles]);

    // Start uploading files and get durations
    startUploading(newUploadFiles);
  }, [uploadFiles]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
  }, [handleFiles]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    handleFiles(files);
    
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [handleFiles]);

  const startUploading = async (filesToUpload: UploadFile[]) => {
    setIsUploading(true);

    for (const uploadFile of filesToUpload) {
      // Get audio duration
      const audio = new Audio();
      const audioUrl = createAudioUrl(uploadFile.file);
      audio.src = audioUrl;
      
      audio.addEventListener('loadedmetadata', () => {
        setFileDurations(prev => new Map(prev.set(uploadFile.id, audio.duration)));
        cleanupAudioUrl(audioUrl);
      });

      try {
        const result = await uploadFileUtil(uploadFile, (progress: number) => {
          setUploadFiles(prev => 
            prev.map(f => 
              f.id === uploadFile.id 
                ? { ...f, progress }
                : f
            )
          );
        });

        setUploadFiles(prev => 
          prev.map(f => 
            f.id === uploadFile.id 
              ? { 
                  ...f, 
                  status: result.success ? 'completed' : 'error',
                  url: result.url,
                  error: result.error,
                  progress: result.success ? 100 : 0
                }
              : f
          )
        );
      } catch {
        setUploadFiles(prev => 
          prev.map(f => 
            f.id === uploadFile.id 
              ? { 
                  ...f, 
                  status: 'error',
                  error: 'Upload failed',
                  progress: 0
                }
              : f
          )
        );
      }
    }

    setIsUploading(false);
  };

  const handleRemoveFile = useCallback((fileId: string) => {
    // Stop audio if playing
    if (playingFileId === fileId) {
      const audio = audioElements.get(fileId);
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }
      setPlayingFileId(null);
    }
    
    // Remove from state
    setUploadFiles(prev => prev.filter(f => f.id !== fileId));
    setFileDurations(prev => {
      const newMap = new Map(prev);
      newMap.delete(fileId);
      return newMap;
    });
    
    // Cleanup audio element
    const audio = audioElements.get(fileId);
    if (audio) {
      const audioUrl = audio.src;
      audio.pause();
      cleanupAudioUrl(audioUrl);
      setAudioElements(prev => {
        const newMap = new Map(prev);
        newMap.delete(fileId);
        return newMap;
      });
    }
  }, [playingFileId, audioElements]);

  const handlePlayPause = useCallback((fileId: string, file: File) => {
    if (playingFileId === fileId) {
      // Pause current file
      const audio = audioElements.get(fileId);
      if (audio) {
        audio.pause();
      }
      setPlayingFileId(null);
    } else {
      // Stop any currently playing audio
      if (playingFileId) {
        const currentAudio = audioElements.get(playingFileId);
        if (currentAudio) {
          currentAudio.pause();
          currentAudio.currentTime = 0;
        }
      }

      // Play new file
      let audio = audioElements.get(fileId);
      if (!audio) {
        audio = new Audio();
        const audioUrl = createAudioUrl(file);
        audio.src = audioUrl;
        
        audio.addEventListener('ended', () => {
          setPlayingFileId(null);
        });
        
        setAudioElements(prev => new Map(prev.set(fileId, audio!)));
      }
      
      audio.play();
      setPlayingFileId(fileId);
    }
  }, [playingFileId, audioElements]);

  const handleDownload = useCallback((uploadFile: UploadFile) => {
    const url = createAudioUrl(uploadFile.file);
    const link = document.createElement('a');
    link.href = url;
    link.download = uploadFile.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    cleanupAudioUrl(url);
  }, []);

  const handleSubmit = () => {
    const completedFiles = uploadFiles.filter(f => f.status === 'completed');
    if (completedFiles.length > 0) {
      setIsSubmitted(true);
      onSubmit(completedFiles);
    }
  };

  const handleClose = () => {
    // Cleanup all audio elements
    audioElements.forEach((audio) => {
      audio.pause();
      const audioUrl = audio.src;
      cleanupAudioUrl(audioUrl);
    });

    setUploadFiles([]);
    setErrors([]);
    setIsUploading(false);
    setApplicationMessage('');
    setPlayingFileId(null);
    setAudioElements(new Map());
    setFileDurations(new Map());
    setIsSubmitted(false);
    onClose();
  };

  const completedFiles = uploadFiles.filter(f => f.status === 'completed');
  const canSubmit = completedFiles.length > 0 && !isUploading;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className="max-w-2xl max-h-[90vh] overflow-hidden p-0"
        style={cssVariables}
        data-theme={themeMode}
      >
        {isSubmitted ? (
          /* Success Screen */
          <div className="flex flex-col items-center justify-center p-12 text-center space-y-6">
            <div className="w-16 h-16 bg-pink-500 rounded-full flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-white" />
            </div>
            <div className="space-y-2">
              <h2 className="text-xl font-bold text-foreground">
                Yay, Your application is submitted successfully
              </h2>
              <p className="text-sm text-muted-foreground max-w-md">
                Thank you for submitting your ATTENTION verse! We&apos;re excited to review your submission and will get back to you soon. Stay tuned and keep making music!
              </p>
            </div>
            <Button
              onClick={handleClose}
              className="bg-primary hover:bg-primary/90 text-primary-foreground px-8"
            >
              Done
            </Button>
          </div>
        ) : (
          <>
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-border">
              <div className="flex flex-col items-start gap-3">
                <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                  <span className="text-primary-foreground text-lg font-bold"><Disc/></span>
                </div>
                <div>
                  <h2 className="text-xl font-bold text-foreground">Apply for Open Verse Contest</h2>
                  <p className="text-sm text-muted-foreground">Upload your recording to SMASH and share it with the world.</p>
                </div>
              </div>
            </div>

        {/* Content */}
        <div className="p-6 space-y-6 max-h-[calc(90vh-140px)] overflow-y-auto">
          {/* Application Message */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Application Message (Optional)</label>
            <Textarea
              placeholder="Tell the artist why you're perfect for this gig"
              value={applicationMessage}
              onChange={(e) => {
                if (e.target.value.length <= 1000) {
                  setApplicationMessage(e.target.value);
                }
              }}
              className="min-h-[100px] resize-none border-input focus:border-ring focus:ring-ring"
              maxLength={1000}
            />
            <div className="text-xs text-muted-foreground text-right">
              {applicationMessage.length}/1000 characters
            </div>
          </div>

          {/* Upload Section */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Upload your Remix File(s)</label>

            {/* Upload Area */}
            <div
              className={cn(
                "border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer",
                isDragOver
                  ? "border-primary bg-primary/5"
                  : "border-border hover:border-primary/50"
              )}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <div className="flex flex-col items-center gap-4">
                <Upload className="h-12 w-12 text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-muted-foreground">
                    Drag & drop your audio files or click to browse
                  </p>
                  <Button
                    className="bg-primary hover:bg-primary/90 text-primary-foreground px-6"
                  >
                    Select Files
                  </Button>
                  <p className="text-xs text-muted-foreground">
                    Supports: AIF, AIFF, WAV, MP3, MP4, AAC
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* File List */}
          {uploadFiles.length > 0 && (
            <div className="space-y-3">
              {uploadFiles.map((uploadFile) => {
                const duration = fileDurations.get(uploadFile.id);
                const isPlaying = playingFileId === uploadFile.id;

                return (
                  <div
                    key={uploadFile.id}
                    className="flex items-center gap-3 p-3 border border-border rounded-lg"
                  >
                    {/* File Icon */}
                    <div className="w-10 h-10 bg-primary rounded flex items-center justify-center flex-shrink-0">
                      <span className="text-primary-foreground text-xs font-bold">
                        {uploadFile.name.split('.').pop()?.toUpperCase()}
                      </span>
                    </div>

                    {/* File Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-foreground truncate">
                          {uploadFile.name}
                        </h4>
                        <div className="flex items-center gap-2 ml-2">
                          <span className="text-xs text-muted-foreground">
                            {formatFileSize(uploadFile.file.size)}
                          </span>
                          {duration && (
                            <>
                              <span className="text-xs text-muted-foreground">•</span>
                              <span className="text-xs text-muted-foreground">
                                {formatDuration(duration)}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-1">
                      {/* Play/Pause Button */}
                      {uploadFile.status === 'completed' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePlayPause(uploadFile.id, uploadFile.file)}
                          className="h-8 w-8 p-0 text-primary hover:text-primary/80"
                        >
                          {isPlaying ? (
                            <Pause className="h-4 w-4" />
                          ) : (
                            <Play className="h-4 w-4" />
                          )}
                        </Button>
                      )}

                      {/* Download Button */}
                      {uploadFile.status === 'completed' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDownload(uploadFile)}
                          className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      )}

                      {/* Delete Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveFile(uploadFile.id)}
                        className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* Errors */}
          {errors.length > 0 && (
            <div className="space-y-2">
              {errors.map((error, index) => (
                <div key={index} className="text-sm text-foreground p-2 rounded">
                  {error}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-border bg-muted/30">
          <Button
            variant="outline"
            onClick={handleClose}
            className="px-6"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!canSubmit}
            className="bg-primary hover:bg-primary/90 text-primary-foreground px-6"
          >
            Submit Track
          </Button>
        </div>

            {/* Hidden File Input */}
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".aif,.aiff,.wav,.mp3,.mp4,.aac,audio/wav,audio/mpeg,audio/mp3,audio/aiff,audio/x-aiff,audio/mp4,audio/aac"
              onChange={handleFileSelect}
              className="hidden"
            />
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
